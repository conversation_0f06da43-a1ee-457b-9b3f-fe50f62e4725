# COT生成器使用说明

本项目包含两个版本的COT（Chain of Thought，思维链）生成器，用于生成设备故障诊断的思维链推理示例。

## 文件说明

### 1. COT生成器_v2.py
- **功能**: 基于Excel数据的动态COT生成器
- **特点**: 
  - 从Excel文件中读取检查项目数据
  - 动态生成参数值和检测结果
  - 支持自控、井控、电气三种故障类型
  - 生成的内容更加多样化和随机化

### 2. COT生成器_简化版.py
- **功能**: 基于预定义模板的简化COT生成器
- **特点**:
  - 使用预定义的场景模板
  - 生成格式更接近COT示例.docx中的格式
  - 推理逻辑更加清晰和一致
  - 适合生成标准化的训练数据

### 3. COT示例.docx
- **功能**: 原始示例文档
- **内容**: 包含两个标准的COT示例，展示了期望的输出格式

## 生成的COT格式

每个生成的COT示例包含以下三个核心部分：

```json
{
  "question": "专家角色设定 + 检测信息 + 任务要求",
  "thinking": "逐步推理过程，分析每个检测指标",
  "conclusion": "最终结论或进一步排查建议"
}
```

### 示例格式对比

**原始示例格式**:
```
"question": 你是井口安全阀关断报警排查事故场景下的专家，请根据下面已知的检测信息确认故障发生的原因；如果已知信息不足以检测到故障的发生原因，请提供进一步排查的方向：已知故障发生后，分离器入口压力值为3.7MPa，井场出口温度为84.2℃，易熔塞压力值为: 40.4psi。

"thinking": 分离器入口压力值的报警阈值为15Mpa，如果分离器入口压力值小于15Mpa时，分离器状态正常；如果分离器入口压力值大于15psi时，会触发安全阀报警。当前分离器入口压力值为3.7psi，小于报警阈值，因此不会导致安全阀关断报警。...

"conclusion": "易熔塞压力值低于报警阈值，因此压力开关接头损坏或压力异常可能是导致安全阀关断的原因"。
```

## 使用方法

### 环境要求
```bash
pip install pandas openpyxl
```

### 运行COT生成器_v2.py
```bash
python COT生成器_v2.py
```
- 生成5个随机的COT示例
- 输出文件: `COT示例_v2_YYYYMMDD_HHMMSS.json`

### 运行COT生成器_简化版.py
```bash
python COT生成器_简化版.py
```
- 生成6个基于模板的COT示例
- 输出文件: `COT示例_简化版_YYYYMMDD_HHMMSS.json`

## 故障类型说明

### 1. 自控类故障
- **检查项目**: 分离器压力、井下/地面安全阀压力、温度、ESD按钮、远程命令等
- **专家角色**: 井口安全阀关断报警排查事故场景下的专家
- **典型故障**: 压力超限、温度异常、人为操作等

### 2. 井控类故障  
- **检查项目**: 针阀接头状态、外漏检查、易熔塞状态等
- **专家角色**: 井控系统故障诊断专家
- **典型故障**: 针阀损坏、管道外漏、密封失效等

### 3. 电气类故障
- **检查项目**: 现场电压、配电柜接线、继电器状态等
- **专家角色**: 电气系统故障排查专家
- **典型故障**: 电压异常、接线松动、继电器故障等

## 输出文件结构

生成的JSON文件包含以下字段：
- `question`: 问题描述
- `thinking`: 推理过程
- `conclusion`: 结论
- `fault_type`: 故障类型（自控/井控/电气）
- `detection_data`: 检测数据
- `fault_cause`: 故障原因（如果有）
- `generation_time`: 生成时间
- `example_id`: 示例编号

## 推荐使用场景

### 使用COT生成器_v2.py当：
- 需要大量多样化的训练数据
- 希望参数值随机变化
- 需要测试模型对不同数值的适应性

### 使用COT生成器_简化版.py当：
- 需要标准化的示例格式
- 希望推理逻辑清晰一致
- 用于演示或教学目的
- 需要与原始示例格式保持高度一致

## 注意事项

1. 确保`设备排查指标及推论.xlsx`文件存在于同一目录
2. Excel文件应包含`检查项目清单`工作表
3. 生成的JSON文件使用UTF-8编码
4. 可以根据需要修改专家角色和场景模板

## 扩展建议

- 可以添加更多故障类型和场景模板
- 可以集成更复杂的参数关联逻辑
- 可以添加多语言支持
- 可以集成到更大的AI训练流水线中
