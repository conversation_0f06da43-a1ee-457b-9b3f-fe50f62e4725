#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COT生成器 - 修复版
修复了原版本的问题，添加了多样化的无故障结论
"""

import pandas as pd
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class InspectionItem:
    """检查项目数据类"""
    item_id: str
    operation_type: str
    item_name: str
    param_definition: str
    judgment_condition: str
    result_template: str
    reasoning_normal: str
    reasoning_abnormal: str
    fault_type: str
    priority: int

class COTGeneratorFixed:
    """修复版COT生成器"""
    
    def __init__(self, excel_file: str = "设备排查指标及推论.xlsx"):
        """初始化生成器"""
        self.excel_file = excel_file
        self.inspection_items = self._load_inspection_items()
        self.parameter_ranges = self._define_parameter_ranges()
        self.expert_roles = {
            "自控": "井口安全阀关断报警排查事故场景下的专家",
            "井控": "井控系统故障诊断专家", 
            "电气": "电气系统故障排查专家"
        }
        
    def _load_inspection_items(self) -> Dict[str, List[InspectionItem]]:
        """加载检查项目数据"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='检查项目清单')
            items_by_type = {"自控": [], "井控": [], "电气": []}
            
            for _, row in df.iterrows():
                item = InspectionItem(
                    item_id=row['检查项目ID'],
                    operation_type=row['操作类型'],
                    item_name=row['检查项目名称'],
                    param_definition=row['参数定义'],
                    judgment_condition=row['判断条件'],
                    result_template=row['结果模板'],
                    reasoning_normal=row['推理模板_正常'],
                    reasoning_abnormal=row['推理模板_异常'],
                    fault_type=row['故障类型'],
                    priority=row['优先级'],
                )
                items_by_type[row['故障类型']].append(item)
                
            return items_by_type
            
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            return {"自控": [], "井控": [], "电气": []}
    
    def _define_parameter_ranges(self) -> Dict[str, Dict]:
        """定义参数范围"""
        return {
            "pressure": {"normal": (5, 14), "abnormal": (15, 25), "unit": "MPa", "threshold": 15},
            "downhole_pressure": {"normal": (5000, 19999), "abnormal": (20001, 25000), "unit": "kPa", "threshold": 20000},
            "surface_pressure": {"normal": (2000, 4999), "abnormal": (5001, 7000), "unit": "kPa", "threshold": 5000},
            "fusible_pressure": {"normal": (51, 80), "abnormal": (30, 49), "unit": "psi", "threshold": 50},
            "temperature": {"normal": (25, 94), "abnormal": (96, 115), "unit": "℃", "threshold": 95},
            "voltage": {"normal": (220, 240), "abnormal": (200, 219), "unit": "V", "threshold": 220}
        }
    
    def _generate_parameter_value(self, param_type: str, is_abnormal: bool = False) -> Tuple[float, str, float]:
        """生成参数值，返回值、单位和阈值"""
        if param_type not in self.parameter_ranges:
            return 0.0, "", 0.0
            
        config = self.parameter_ranges[param_type]
        range_key = "abnormal" if is_abnormal else "normal"
        min_val, max_val = config[range_key]
        value = round(random.uniform(min_val, max_val), 1)
        
        return value, config["unit"], config["threshold"]
    
    def _generate_no_fault_conclusion(self, fault_type: str) -> str:
        """生成无故障时的多样化结论"""
        conclusions_by_type = {
            "自控": [
                "当前自控系统各项检测指标均在正常范围内。建议进一步检查液压系统压力稳定性、控制回路信号传输质量，以及检验传感器校准状态和数据采集系统的准确性。",
                "初步排查显示自控相关参数无明显异常。建议扩大排查范围至机械部件，重点关注阀门密封性能、管道连接完整性，以及检查是否存在间歇性故障或环境因素影响。",
                "自控系统检测结果暂未发现异常指标。建议从系统集成角度分析，检查各子系统间的协调性、通信协议的稳定性，以及考虑负载变化对系统性能的潜在影响。",
                "当前自控检测数据显示系统运行正常。建议进行深度诊断，包括历史数据趋势分析、预防性维护检查，以及评估操作程序是否存在人为因素导致的隐患。",
                "自控系统表面检测无异常，但故障可能源于其他系统。建议交叉验证井控和电气系统状态，重点排查系统间的接口问题和联锁逻辑的正确性。"
            ],
            "井控": [
                "井控系统检查结果显示各项指标正常。建议深入检查井下工具的工作状态、套管完整性，以及地层压力变化对井控设备的影响，同时关注钻井液性能参数。",
                "当前井控相关检测未发现明显故障点。建议从井筒条件角度分析，检查井眼轨迹、地质条件变化，以及评估井控设备在当前工况下的适应性和可靠性。",
                "井控系统表面检测正常，故障原因可能较为隐蔽。建议进行压力测试、密封性检验，重点关注高压部件的疲劳状态和关键密封件的老化程度。",
                "井控检测数据暂无异常，建议扩大诊断范围。重点检查防喷器组合、节流管汇的工作状态，以及井控操作程序的执行规范性和应急响应系统的完备性。",
                "当前井控系统各项参数在正常范围内。建议从预防性角度出发，检查备用设备的可用性、应急预案的有效性，以及操作人员的技能水平和应急处置能力。"
            ],
            "电气": [
                "电气系统检测结果显示参数正常。建议深入检查电源质量、谐波干扰情况，以及UPS系统的工作状态和备用电源的可靠性，确保供电系统的稳定性。",
                "当前电气相关指标无明显异常。建议从电磁兼容性角度分析，检查接地系统完整性、屏蔽措施有效性，以及电气设备间的相互干扰情况。",
                "电气系统表面检测正常，建议进行绝缘性能测试。重点关注高压设备的绝缘状态、电缆老化程度，以及在恶劣环境条件下电气设备的防护等级。",
                "电气检测数据暂未发现故障点。建议检查控制系统的软件版本、程序逻辑的正确性，以及人机界面的操作响应性和数据显示的准确性。",
                "当前电气系统运行参数正常。建议从系统维护角度出发，检查定期保养计划的执行情况、关键部件的更换周期，以及电气安全防护措施的完善程度。"
            ]
        }
        
        # 根据故障类型随机选择一个结论
        available_conclusions = conclusions_by_type.get(fault_type, [
            "当前检测指标无明显异常。建议扩大排查范围，从多个角度进行系统性分析，"
            "包括设备状态、操作程序、环境因素等方面的综合评估。"
        ])
        
        return random.choice(available_conclusions)
    
    def generate_simple_cot(self, fault_type: str = None) -> Dict:
        """生成简化的COT示例"""
        if fault_type is None:
            fault_type = random.choice(["自控", "井控", "电气"])
        
        # 简化的检测数据生成
        detection_scenarios = {
            "自控": [
                {"分离器入口压力": "12.5MPa", "井场出口温度": "75.3℃", "易熔塞压力值": "65.2psi"},
                {"RTU机柜检查": "没有发现人为关断命令", "站控室检查": "没有发现远程ESD命令", "井下安全阀压力": "18000kPa"}
            ],
            "井控": [
                {"井下安全阀针阀状态": "完好", "井下安全阀外漏状态": "无漏", "地面安全阀针阀状态": "完好"},
                {"易熔塞压力开关状态": "完好", "高低压导阀状态": "正常"}
            ],
            "电气": [
                {"现场电压": "225V", "低压配电柜接线状态": "正常", "继电器状态": "正常"},
                {"UPS系统状态": "正常", "接地系统状态": "完好"}
            ]
        }
        
        # 随机选择一个场景
        detection_data = random.choice(detection_scenarios[fault_type])
        
        # 构建问题描述
        expert_role = self.expert_roles[fault_type]
        detection_info = "、".join([f"{k}为{v}" for k, v in detection_data.items()])
        
        question = (
            f"你是{expert_role}，请根据下面已知的检测信息确认故障发生的原因；"
            f"如果已知信息不足以检测到故障的发生原因，请提供进一步排查的方向："
            f"已知故障发生后，{detection_info}。"
        )
        
        # 简化的thinking生成
        thinking_parts = []
        for key, value in detection_data.items():
            if "压力" in key:
                thinking_parts.append(f"根据检测数据，{key}为{value}，在正常范围内，不会导致故障。")
            elif "温度" in key:
                thinking_parts.append(f"检测显示{key}为{value}，低于报警阈值，系统温度正常。")
            elif "状态" in key:
                thinking_parts.append(f"检查结果显示{key}为{value}，设备工作正常。")
            else:
                thinking_parts.append(f"排查发现{key}为{value}，未发现异常情况。")
        
        thinking = "".join(thinking_parts)
        
        # 生成多样化结论
        conclusion = self._generate_no_fault_conclusion(fault_type)
        
        return {
            "question": question,
            "thinking": thinking,
            "conclusion": conclusion,
            "fault_type": fault_type,
            "detection_data": detection_data,
            "fault_cause": None,
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def generate_multiple_examples(self, num_examples: int = 5) -> List[Dict]:
        """生成多个COT示例"""
        examples = []
        fault_types = ["自控", "井控", "电气"]
        
        for i in range(num_examples):
            fault_type = fault_types[i % len(fault_types)]
            example = self.generate_simple_cot(fault_type)
            example["example_id"] = i + 1
            examples.append(example)
        
        return examples
    
    def export_to_json(self, examples: List[Dict], filename: str = None) -> str:
        """导出示例到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"COT示例_修复版_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(examples, f, ensure_ascii=False, indent=2)
        
        return filename
    
    def print_example(self, example: Dict):
        """打印单个示例"""
        print(f"示例{example.get('example_id', '')}：")
        print(f'"question": {example["question"]}')
        print(f'"thinking": {example["thinking"]}')
        print(f'"conclusion": {example["conclusion"]}')
        print()

def main():
    """主函数"""
    print("=== COT生成器 - 修复版 ===")
    
    # 创建生成器实例
    generator = COTGeneratorFixed()
    
    # 生成多个示例
    print("正在生成COT示例...")
    examples = generator.generate_multiple_examples(20)
    
    # 显示生成的示例
    for example in examples:
        generator.print_example(example)
    
    # 导出到JSON文件
    json_file = generator.export_to_json(examples)
    print(f"示例已导出到: {json_file}")

if __name__ == "__main__":
    main()
