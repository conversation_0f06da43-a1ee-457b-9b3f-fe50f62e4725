#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COT生成器 v2.0 - 基于示例格式的思维链生成器
生成类似COT示例.docx中格式的思维链推理
"""

import pandas as pd
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class InspectionItem:
    """检查项目数据类"""
    item_id: str
    operation_type: str
    item_name: str
    param_definition: str
    judgment_condition: str
    result_template: str
    reasoning_normal: str
    reasoning_abnormal: str
    fault_type: str
    priority: int

class COTGeneratorV2:
    """COT生成器 v2.0 - 基于示例格式"""
    
    def __init__(self, excel_file: str = "设备排查指标及推论.xlsx"):
        """初始化生成器"""
        self.excel_file = excel_file
        self.inspection_items = self._load_inspection_items()
        self.parameter_ranges = self._define_parameter_ranges()
        self.expert_roles = {
            "自控": "井口安全阀关断报警排查事故场景下的专家",
            "井控": "井控系统故障诊断专家", 
            "电气": "电气系统故障排查专家"
        }
        
    def _load_inspection_items(self) -> Dict[str, List[InspectionItem]]:
        """加载检查项目数据"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='检查项目清单')
            items_by_type = {"自控": [], "井控": [], "电气": []}
            
            for _, row in df.iterrows():
                item = InspectionItem(
                    item_id=row['检查项目ID'],
                    operation_type=row['操作类型'],
                    item_name=row['检查项目名称'],
                    param_definition=row['参数定义'],
                    judgment_condition=row['判断条件'],
                    result_template=row['结果模板'],
                    reasoning_normal=row['推理模板_正常'],
                    reasoning_abnormal=row['推理模板_异常'],
                    fault_type=row['故障类型'],
                    priority=row['优先级'],
                )
                items_by_type[row['故障类型']].append(item)
                
            return items_by_type
            
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            return {"自控": [], "井控": [], "电气": []}
    
    def _define_parameter_ranges(self) -> Dict[str, Dict]:
        """定义参数范围"""
        return {
            "pressure": {"normal": (5, 14), "abnormal": (15, 25), "unit": "MPa", "threshold": 15},
            "downhole_pressure": {"normal": (5000, 19999), "abnormal": (20001, 25000), "unit": "kPa", "threshold": 20000},
            "surface_pressure": {"normal": (2000, 4999), "abnormal": (5001, 7000), "unit": "kPa", "threshold": 5000},
            "fusible_pressure": {"normal": (51, 80), "abnormal": (30, 49), "unit": "psi", "threshold": 50},
            "temperature": {"normal": (25, 94), "abnormal": (96, 115), "unit": "℃", "threshold": 95},
            "voltage": {"normal": (220, 240), "abnormal": (200, 219), "unit": "V", "threshold": 220}
        }
    
    def _generate_parameter_value(self, param_type: str, is_abnormal: bool = False) -> Tuple[float, str, float]:
        """生成参数值，返回值、单位和阈值"""
        if param_type not in self.parameter_ranges:
            return 0.0, "", 0.0
            
        config = self.parameter_ranges[param_type]
        range_key = "abnormal" if is_abnormal else "normal"
        min_val, max_val = config[range_key]
        value = round(random.uniform(min_val, max_val), 1)
        
        return value, config["unit"], config["threshold"]
    
    def _generate_detection_data(self, fault_type: str, num_items: int = 3) -> Dict:
        """生成检测数据"""
        available_items = self.inspection_items.get(fault_type, [])
        if not available_items:
            return {}
            
        selected_items = random.sample(available_items, min(num_items, len(available_items)))
        
        # 随机选择一个异常项目
        fault_item_index = random.randint(0, len(selected_items) - 1)
        
        detection_data = {}
        fault_cause = None
        
        for i, item in enumerate(selected_items):
            is_fault_item = (i == fault_item_index)
            
            if "压力" in item.param_definition:
                if "分离器" in item.item_name:
                    value, unit, threshold = self._generate_parameter_value("pressure", is_fault_item)
                    detection_data["分离器入口压力"] = f"{value}{unit}"
                    if is_fault_item:
                        fault_cause = "分离器入口压力超限"
                        
                elif "井下" in item.item_name:
                    value, unit, threshold = self._generate_parameter_value("downhole_pressure", is_fault_item)
                    detection_data["井下安全阀压力"] = f"{value}{unit}"
                    if is_fault_item:
                        fault_cause = "井下安全阀压力超限"
                        
                elif "地面" in item.item_name:
                    value, unit, threshold = self._generate_parameter_value("surface_pressure", is_fault_item)
                    detection_data["地面安全阀压力"] = f"{value}{unit}"
                    if is_fault_item:
                        fault_cause = "地面安全阀压力超限"
                        
                elif "易熔塞" in item.item_name:
                    value, unit, threshold = self._generate_parameter_value("fusible_pressure", is_fault_item)
                    detection_data["易熔塞压力值"] = f"{value}{unit}"
                    if is_fault_item:
                        fault_cause = "易熔塞压力异常，压力开关接头损坏或压力异常"
                        
            elif "温度" in item.param_definition:
                value, unit, threshold = self._generate_parameter_value("temperature", is_fault_item)
                detection_data["井场出口温度"] = f"{value}{unit}"
                if is_fault_item:
                    fault_cause = "井场出口温度超限"
                    
            elif "布尔值" in item.param_definition:
                if "ESD按钮" in item.item_name:
                    status = "发现人为关断命令" if is_fault_item else "没有发现人为关断命令"
                    detection_data["RTU机柜检查"] = status
                    if is_fault_item:
                        fault_cause = "人为现场操作导致安全阀关断"

                elif "命令" in item.item_name:
                    status = "发现远程ESD命令" if is_fault_item else "没有发现远程ESD命令"
                    detection_data["站控室检查"] = status
                    if is_fault_item:
                        fault_cause = "人为远程操作导致安全阀关断"

                elif "针阀" in item.item_name or "接头" in item.item_name:
                    # 井控类型的针阀接头检查
                    valve_status = "损坏" if is_fault_item else "完好"
                    leak_status = "有漏" if is_fault_item else "无漏"
                    detection_data[f"{item.item_name}状态"] = f"针阀接头{valve_status}，外漏状态{leak_status}"
                    if is_fault_item:
                        fault_cause = f"{item.item_name}故障"

                elif "易熔塞" in item.item_name:
                    # 井控类型的易熔塞检查
                    status = "损坏" if is_fault_item else "完好"
                    detection_data["易熔塞压力开关状态"] = status
                    if is_fault_item:
                        fault_cause = "易熔塞压力开关故障"

                elif "电压" in item.param_definition or "配电" in item.item_name:
                    # 电气类型检查
                    voltage_val, unit, threshold = self._generate_parameter_value("voltage", is_fault_item)
                    detection_data["现场电压"] = f"{voltage_val}{unit}"
                    if is_fault_item:
                        fault_cause = "电压异常"

                elif "接线" in item.item_name or "继电器" in item.item_name:
                    # 电气类型的接线检查
                    status = "松动" if is_fault_item else "正常"
                    detection_data[f"{item.item_name}状态"] = status
                    if is_fault_item:
                        fault_cause = f"{item.item_name}故障"
        
        return {
            "detection_data": detection_data,
            "fault_cause": fault_cause,
            "selected_items": selected_items
        }

    def _generate_thinking_process(self, detection_data: Dict, selected_items: List, fault_cause: str) -> str:
        """生成thinking推理过程"""
        thinking_parts = []
        processed_keys = set()  # 避免重复处理相同的检测数据

        for item in selected_items:
            if "压力" in item.param_definition:
                if "分离器" in item.item_name and "分离器入口压力" in detection_data:
                    pressure_str = detection_data["分离器入口压力"]
                    pressure_val = float(pressure_str.replace("MPa", ""))
                    threshold = self.parameter_ranges["pressure"]["threshold"]

                    thinking_parts.append(
                        f"分离器入口压力值的报警阈值为{threshold}MPa，如果分离器入口压力值小于{threshold}MPa时，分离器状态正常；"
                        f"如果分离器入口压力值大于{threshold}MPa时，会触发安全阀报警。"
                        f"当前分离器入口压力值为{pressure_val}MPa，"
                        f"{'大于' if pressure_val > threshold else '小于'}报警阈值，"
                        f"因此{'会' if pressure_val > threshold else '不会'}导致安全阀关断报警。"
                    )

                elif "井下" in item.item_name and "井下安全阀压力" in detection_data:
                    pressure_str = detection_data["井下安全阀压力"]
                    pressure_val = float(pressure_str.replace("kPa", ""))
                    threshold = self.parameter_ranges["downhole_pressure"]["threshold"]

                    thinking_parts.append(
                        f"井下安全阀的阈值为{threshold}kPa，如果井下安全阀压力大于{threshold}kPa，则会引起井口安全阀关断，"
                        f"如果井下安全阀压力小于{threshold}kPa，则不会引起井口安全阀关断。"
                        f"当前井下安全阀压力值为{pressure_val}kPa，"
                        f"说明安全阀关断{'是' if pressure_val > threshold else '不是'}因为井下安全阀压力超限导致的。"
                    )

                elif "地面" in item.item_name and "地面安全阀压力" in detection_data:
                    pressure_str = detection_data["地面安全阀压力"]
                    pressure_val = float(pressure_str.replace("kPa", ""))
                    threshold = self.parameter_ranges["surface_pressure"]["threshold"]

                    thinking_parts.append(
                        f"地面安全阀的阈值为{threshold}kPa，如果地面安全阀压力大于{threshold}kPa，则会引起井口安全阀关断，"
                        f"如果地面安全阀压力小于{threshold}kPa，则不会引起井口安全阀关断。"
                        f"当前地面安全阀压力值为{pressure_val}kPa，"
                        f"说明安全阀关断{'是' if pressure_val > threshold else '不是'}因为地面安全阀压力超限导致的。"
                    )

                elif "易熔塞" in item.item_name and "易熔塞压力值" in detection_data:
                    pressure_str = detection_data["易熔塞压力值"]
                    pressure_val = float(pressure_str.replace("psi", ""))
                    threshold = self.parameter_ranges["fusible_pressure"]["threshold"]

                    thinking_parts.append(
                        f"易熔塞的报警阈值为{threshold}psi，如果易熔塞压力值大于{threshold}psi时，易熔塞状态正常；"
                        f"如果易熔塞压力值小于{threshold}psi时，会触发安全阀报警。"
                        f"当前易熔塞压力值为{pressure_val}psi，"
                        f"{'小于' if pressure_val < threshold else '大于'}报警阈值，"
                        f"因此{'会' if pressure_val < threshold else '不会'}导致安全阀关断报警。"
                    )

            elif "温度" in item.param_definition and "井场出口温度" in detection_data:
                temp_str = detection_data["井场出口温度"]
                temp_val = float(temp_str.replace("℃", ""))
                threshold = self.parameter_ranges["temperature"]["threshold"]

                thinking_parts.append(
                    f"井口出口温度的报警阈值为{threshold}℃，如果井口出口温度的值低于{threshold}℃时，温度状态正常；"
                    f"如果井口出口温度值高于{threshold}℃时，会触发安全阀报警。"
                    f"当前井口出口温度的值为{temp_val}℃，"
                    f"{'大于' if temp_val > threshold else '小于'}报警阈值，"
                    f"因此{'会' if temp_val > threshold else '不会'}导致安全阀关断报警。"
                )

            elif "布尔值" in item.param_definition:
                if "ESD按钮" in item.item_name and "RTU机柜检查" in detection_data:
                    status = detection_data["RTU机柜检查"]
                    thinking_parts.append(
                        f"如果RTU机柜上发现了人为关断命令，则说明是人为现场操作导致的安全阀关断；"
                        f"如果RTU机柜上没有发现人为关断命令，则说明安全阀关断不是由于人为现场操作导致的。"
                        f"当前{status}，说明安全阀关断{'是' if '发现' in status else '不是'}由于人为现场操作导致的。"
                    )

                elif "命令" in item.item_name and "站控室检查" in detection_data:
                    status = detection_data["站控室检查"]
                    thinking_parts.append(
                        f"如果站控室里发现了远程ESD命令，则说明是人为远程操作导致的安全阀关断；"
                        f"如果站控室里没有发现远程ESD命令，则说明安全阀关断不是由于人为远程操作导致的。"
                        f"当前{status}，说明安全阀关断{'是' if '发现' in status else '不是'}由于人为远程操作导致的。"
                    )

            # 处理井控和电气类型的布尔值检查
            for key, value in detection_data.items():
                if key in processed_keys:
                    continue
                processed_keys.add(key)

                if "状态" in key:
                    if "针阀接头" in value:
                        thinking_parts.append(
                            f"针阀接头的完好性直接影响系统密封性能，如果针阀接头损坏或存在外漏，"
                            f"会导致压力异常进而触发安全阀关断。当前{key}为{value}，"
                            f"{'会' if '损坏' in value or '有漏' in value else '不会'}导致安全阀关断。"
                        )
                    elif "易熔塞" in key:
                        thinking_parts.append(
                            f"易熔塞压力开关是重要的安全保护装置，如果易熔塞压力开关损坏，"
                            f"会影响压力监测的准确性。当前{key}为{value}，"
                            f"{'会' if '损坏' in value else '不会'}影响系统正常运行。"
                        )
                    elif "电压" in key or "接线" in key or "继电器" in key:
                        thinking_parts.append(
                            f"电气系统的稳定性对安全阀控制至关重要，如果存在电压异常、接线松动或继电器故障，"
                            f"会影响控制信号的传输。当前{key}为{value}，"
                            f"{'会' if '异常' in value or '松动' in value or '故障' in value else '不会'}影响安全阀正常工作。"
                        )
                elif "现场电压" in key:
                    voltage_val = float(value.replace("V", ""))
                    threshold = 220
                    thinking_parts.append(
                        f"现场电压的正常范围应在220-240V之间，如果电压过低会影响设备正常工作。"
                        f"当前现场电压为{voltage_val}V，"
                        f"{'低于' if voltage_val < threshold else '在'}正常范围，"
                        f"因此{'会' if voltage_val < threshold else '不会'}导致设备故障。"
                    )

        return "".join(thinking_parts)

    def generate_cot_example(self, fault_type: str = None) -> Dict:
        """生成COT示例格式的思维链"""
        # 随机选择故障类型（如果未指定）
        if fault_type is None:
            fault_type = random.choice(["自控", "井控", "电气"])

        # 生成检测数据
        data_result = self._generate_detection_data(fault_type)
        if not data_result:
            return {"error": f"没有找到{fault_type}类型的检查项目"}

        detection_data = data_result["detection_data"]
        fault_cause = data_result["fault_cause"]
        selected_items = data_result["selected_items"]

        # 构建问题描述
        expert_role = self.expert_roles.get(fault_type, "故障诊断专家")
        detection_info = "、".join([f"{k}为{v}" for k, v in detection_data.items()])

        question = (
            f"你是{expert_role}，请根据下面已知的检测信息确认故障发生的原因；"
            f"如果已知信息不足以检测到故障的发生原因，请提供进一步排查的方向："
            f"已知故障发生后，{detection_info}。"
        )

        # 生成thinking推理过程
        thinking = self._generate_thinking_process(detection_data, selected_items, fault_cause)

        # 生成结论
        if fault_cause:
            conclusion = f'"{fault_cause}"'
        else:
            conclusion = (
                "当前的各项检测指标都无异常情况。为了排查出问题，后续可以从电气和井控角度继续排查，"
                "比如检测现场电压状况、低压配电柜接线检查、易熔塞压力开关状态、高低压导阀及接口检查等等。"
            )

        return {
            "question": question,
            "thinking": thinking,
            "conclusion": conclusion,
            "fault_type": fault_type,
            "detection_data": detection_data,
            "fault_cause": fault_cause,
            "generation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    def generate_multiple_examples(self, num_examples: int = 5) -> List[Dict]:
        """生成多个COT示例"""
        examples = []
        fault_types = ["自控", "井控", "电气"]

        for i in range(num_examples):
            fault_type = fault_types[i % len(fault_types)]
            example = self.generate_cot_example(fault_type)
            example["example_id"] = i + 1
            examples.append(example)

        return examples

    def export_to_json(self, examples: List[Dict], filename: str = None) -> str:
        """导出示例到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"COT示例_v2_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(examples, f, ensure_ascii=False, indent=2)

        return filename

    def print_example(self, example: Dict):
        """打印单个示例"""
        print(f"示例{example.get('example_id', '')}：")
        print(f'"question": {example["question"]}')
        print(f'"thinking": {example["thinking"]}')
        print(f'"conclusion": {example["conclusion"]}')
        print()

def main():
    """主函数"""
    print("=== COT生成器 v2.0 - 基于示例格式 ===")

    # 创建生成器实例
    generator = COTGeneratorV2()

    # 生成多个示例
    print("正在生成COT示例...")
    examples = generator.generate_multiple_examples(100)

    # 显示生成的示例
    for example in examples:
        generator.print_example(example)

    # 导出到JSON文件
    json_file = generator.export_to_json(examples)
    print(f"示例已导出到: {json_file}")

if __name__ == "__main__":
    main()
