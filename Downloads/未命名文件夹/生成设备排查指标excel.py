#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备排查指标及推论优化脚本
用于生成标准化的设备故障诊断数据结构
"""

import pandas as pd
from typing import Dict, List, Tuple
import os

def create_optimized_inspection_data():
    """创建优化后的设备排查指标数据"""
    
    # 定义标准化的检查项目数据
    inspection_data = [
        # 自控类故障检查项目
        {
            "检查项目ID": "AUTO_001",
            "操作类型": "现场查看",
            "检查项目名称": "分离器入口压力开关动作检查",
            "参数定义": "压力开关状态:布尔值(动作/未动作)",
            "判断条件": "压力开关动作=True时为异常",
            "结果模板": "压力开关状态:{SWITCH_STATUS}",
            "推理模板_正常": "压力开关未动作，排除分离器入口压力开关触发ESD的可能性",
            "推理模板_异常": "压力开关已动作，说明分离器入口压力开关触发了ESD导致安全阀关断",
            "故障类型": "自控",
            "优先级": 1,
            "关键指标": "分离器入口压力开关"
        },
        {
            "检查项目ID": "AUTO_002", 
            "操作类型": "现场查看",
            "检查项目名称": "分离器入口压力值检查",
            "参数定义": "入口压力:数值型(MPa),正常范围:0-15MPa,报警阈值:15MPa",
            "判断条件": "入口压力>15MPa时为异常",
            "结果模板": "入口压力值:{PRESSURE_VALUE}MPa,阈值:15MPa,状态:{LIMIT_STATUS}",
            "推理模板_正常": "分离器入口压力未超限，排除压力超限触发安全阀关断的可能性",
            "推理模板_异常": "分离器入口压力超限，可能是触发安全阀关断的原因",
            "故障类型": "自控",
            "优先级": 1,
            "关键指标": "分离器入口压力"
        },
        {
            "检查项目ID": "AUTO_003",
            "操作类型": "现场查看", 
            "检查项目名称": "井下安全阀压力检查",
            "参数定义": "井下压力:数值型(kPa),正常范围:5000-18000kPa,报警阈值:20000kPa",
            "判断条件": "井下压力>20000kPa时为异常",
            "结果模板": "井下安全阀压力:{DOWNHOLE_PRESSURE}kPa,阈值:20000kPa,状态:{LIMIT_STATUS}",
            "推理模板_正常": "井下安全阀压力正常，排除井下压力超限触发关断的可能性",
            "推理模板_异常": "井下安全阀压力超限，可能是触发安全阀关断的原因",
            "故障类型": "自控",
            "优先级": 1,
            "关键指标": "井下安全阀压力"
        },
        {
            "检查项目ID": "AUTO_004",
            "操作类型": "现场查看",
            "检查项目名称": "地面安全阀压力检查", 
            "参数定义": "地面压力:数值型(kPa),正常范围:2000-5000kPa,报警阈值:5500kPa",
            "判断条件": "地面压力>5500kPa时为异常",
            "结果模板": "地面安全阀压力:{SURFACE_PRESSURE}kPa,阈值:5500kPa,状态:{LIMIT_STATUS}",
            "推理模板_正常": "地面安全阀压力正常，排除地面压力超限触发关断的可能性",
            "推理模板_异常": "地面安全阀压力超限，可能是触发安全阀关断的原因",
            "故障类型": "自控",
            "优先级": 1,
            "关键指标": "地面安全阀压力"
        },
        {
            "检查项目ID": "AUTO_005",
            "操作类型": "现场查看",
            "检查项目名称": "井场出口温度检查",
            "参数定义": "出口温度:数值型(℃),正常范围:25-85℃,报警阈值:95℃",
            "判断条件": "出口温度>95℃时为异常",
            "结果模板": "井场出口温度:{TEMPERATURE}℃,阈值:95℃,状态:{LIMIT_STATUS}",
            "推理模板_正常": "井场出口温度正常，排除温度过高导致安全阀关断的可能性",
            "推理模板_异常": "井场出口温度过高，可能是导致安全阀关断的原因",
            "故障类型": "自控",
            "优先级": 2,
            "关键指标": "井场出口温度"
        },
        {
            "检查项目ID": "AUTO_006",
            "操作类型": "现场查看",
            "检查项目名称": "液压控制柜ESD按钮检查",
            "参数定义": "ESD按钮状态:布尔值(按下/未按下)",
            "判断条件": "ESD按钮状态=按下时为异常",
            "结果模板": "ESD按钮状态:{BUTTON_STATUS}",
            "推理模板_正常": "ESD按钮未被按下，排除人为操作液压控制柜触发ESD的可能性",
            "推理模板_异常": "ESD按钮被按下，说明是人为操作液压控制柜触发的ESD关断",
            "故障类型": "自控",
            "优先级": 2,
            "关键指标": "ESD按钮"
        },
        {
            "检查项目ID": "AUTO_007",
            "操作类型": "现场查看",
            "检查项目名称": "RTU机柜人为命令检查",
            "参数定义": "人为命令状态:布尔值(存在/不存在)",
            "判断条件": "人为命令状态=存在时为异常",
            "结果模板": "RTU机柜人为命令:{COMMAND_STATUS}",
            "推理模板_正常": "RTU机柜无人为命令，排除人为操作RTU机柜导致安全阀关断的可能性",
            "推理模板_异常": "RTU机柜存在人为命令，说明是人为操作RTU机柜导致的安全阀关断",
            "故障类型": "自控",
            "优先级": 2,
            "关键指标": "RTU机柜"
        },
        {
            "检查项目ID": "AUTO_008",
            "操作类型": "现场查看",
            "检查项目名称": "站控室远程ESD命令检查",
            "参数定义": "远程ESD命令:布尔值(存在/不存在)",
            "判断条件": "远程ESD命令=存在时为异常",
            "结果模板": "站控室远程ESD命令:{REMOTE_COMMAND_STATUS}",
            "推理模板_正常": "站控室无远程ESD命令，排除站控室人为操作导致安全阀关断的可能性",
            "推理模板_异常": "站控室存在远程ESD命令，说明是站控室人为操作导致的安全阀关断",
            "故障类型": "自控",
            "优先级": 2,
            "关键指标": "远程ESD命令"
        },
        
        # 井控类故障检查项目
        {
            "检查项目ID": "WELL_001",
            "操作类型": "现场查看",
            "检查项目名称": "井下安全阀针阀及接头检查",
            "参数定义": "针阀接头状态:布尔值(完好/损坏),外漏状态:布尔值(无漏/有漏)",
            "判断条件": "针阀接头状态=损坏或外漏状态=有漏时为异常",
            "结果模板": "井下安全阀针阀接头:{VALVE_STATUS},外漏情况:{LEAK_STATUS}",
            "推理模板_正常": "井下安全阀针阀及接头完好无外漏，排除井下安全阀硬件故障的可能性",
            "推理模板_异常": "井下安全阀针阀及接头损坏或外漏，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 1,
            "关键指标": "井下安全阀针阀"
        },
        {
            "检查项目ID": "WELL_002",
            "操作类型": "现场查看",
            "检查项目名称": "地面安全阀针阀及接头检查",
            "参数定义": "针阀接头状态:布尔值(完好/损坏),外漏状态:布尔值(无漏/有漏)",
            "判断条件": "针阀接头状态=损坏或外漏状态=有漏时为异常",
            "结果模板": "地面安全阀针阀接头:{VALVE_STATUS},外漏情况:{LEAK_STATUS}",
            "推理模板_正常": "地面安全阀针阀及接头完好无外漏，排除地面安全阀硬件故障的可能性",
            "推理模板_异常": "地面安全阀针阀及接头损坏或外漏，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 1,
            "关键指标": "地面安全阀针阀"
        },
        {
            "检查项目ID": "WELL_003",
            "操作类型": "现场查看",
            "检查项目名称": "高低压导阀及接头检查",
            "参数定义": "导阀接头状态:布尔值(完好/损坏),外漏状态:布尔值(无漏/有漏)",
            "判断条件": "导阀接头状态=损坏或外漏状态=有漏时为异常",
            "结果模板": "高低压导阀接头:{VALVE_STATUS},外漏情况:{LEAK_STATUS}",
            "推理模板_正常": "高低压导阀及接头完好无外漏，排除导阀故障的可能性",
            "推理模板_异常": "高低压导阀及接头损坏或外漏，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 2,
            "关键指标": "高低压导阀"
        },
        {
            "检查项目ID": "WELL_004",
            "操作类型": "现场查看",
            "检查项目名称": "液控柜液压管线检查",
            "参数定义": "液压管线状态:布尔值(完好/损坏),外漏状态:布尔值(无漏/有漏)",
            "判断条件": "液压管线状态=损坏或外漏状态=有漏时为异常",
            "结果模板": "液控柜液压管线:{PIPELINE_STATUS},外漏情况:{LEAK_STATUS}",
            "推理模板_正常": "液控柜液压管线及接头完好无外漏，排除液控柜内部故障的可能性",
            "推理模板_异常": "液控柜液压管线及接头损坏或外漏，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 1,
            "关键指标": "液控柜液压管线"
        },
        {
            "检查项目ID": "WELL_005",
            "操作类型": "现场查看",
            "检查项目名称": "易熔塞压力开关检查",
            "参数定义": "易熔塞压力:数值型(psi),正常阈值:>50psi,接头状态:布尔值(完好/损坏)",
            "判断条件": "易熔塞压力<=50psi或接头状态=损坏时为异常",
            "结果模板": "易熔塞压力:{FUSIBLE_PRESSURE}psi,阈值:50psi,接头状态:{JOINT_STATUS}",
            "推理模板_正常": "易熔塞压力开关接头完好且压力正常，排除易熔塞故障的可能性",
            "推理模板_异常": "易熔塞压力开关接头损坏或压力异常，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 2,
            "关键指标": "易熔塞压力开关"
        },
        {
            "检查项目ID": "WELL_006",
            "操作类型": "现场查看",
            "检查项目名称": "井口液压管线检查",
            "参数定义": "液压管线状态:布尔值(完好/损坏),渗漏状态:布尔值(无漏/有漏)",
            "判断条件": "液压管线状态=损坏或渗漏状态=有漏时为异常",
            "结果模板": "井口液压管线:{PIPELINE_STATUS},渗漏情况:{LEAK_STATUS}",
            "推理模板_正常": "井口液压管线及接头完好无渗漏，排除井口液压系统故障的可能性",
            "推理模板_异常": "井口液压管线及接头损坏或渗漏，可能是导致安全阀关断的原因",
            "故障类型": "井控",
            "优先级": 2,
            "关键指标": "井口液压管线"
        },
        {
            "检查项目ID": "WELL_007",
            "操作类型": "现场查看",
            "检查项目名称": "液控柜压力稳定性测试",
            "参数定义": "压力稳定性:布尔值(稳定/不稳定),掉压状态:布尔值(无掉压/有掉压)",
            "判断条件": "压力稳定性=不稳定或掉压状态=有掉压时为异常",
            "结果模板": "液控柜压力稳定性:{PRESSURE_STABILITY},掉压状态:{PRESSURE_DROP}",
            "推理模板_正常": "液控柜各项压力指标稳定不掉压，排除液控柜内组件故障的可能性",
            "推理模板_异常": "液控柜压力不稳定或存在掉压，可能是液控柜内组件故障导致的",
            "故障类型": "井控",
            "优先级": 1,
            "关键指标": "液控柜压力稳定性"
        },
        
        # 电气类故障检查项目
        {
            "检查项目ID": "ELEC_001",
            "操作类型": "现场查看",
            "检查项目名称": "UPS模块供电电压检查",
            "参数定义": "供电电压:数值型(V),正常范围:220-240V,继电器接线:布尔值(正常/松动)",
            "判断条件": "供电电压<220V或>240V或继电器接线=松动时为异常",
            "结果模板": "UPS供电电压:{VOLTAGE}V,正常范围:220-240V,继电器接线:{RELAY_STATUS}",
            "推理模板_正常": "UPS供电电压稳定且继电器接线正常，排除UPS供电故障的可能性",
            "推理模板_异常": "UPS供电电压异常或继电器接线松动，可能导致继电器断电触发安全阀关断",
            "故障类型": "电气",
            "优先级": 1,
            "关键指标": "UPS供电电压"
        },
        {
            "检查项目ID": "ELEC_002",
            "操作类型": "现场查看",
            "检查项目名称": "低压配电柜接线检查",
            "参数定义": "接线状态:布尔值(正常/松动),虚接状态:布尔值(无虚接/有虚接)",
            "判断条件": "接线状态=松动或虚接状态=有虚接时为异常",
            "结果模板": "配电柜接线状态:{WIRING_STATUS},虚接情况:{LOOSE_CONNECTION}",
            "推理模板_正常": "低压配电柜接线正常无虚接，排除配电柜接线故障的可能性",
            "推理模板_异常": "低压配电柜接线松动或虚接，可能是导致安全阀关断的原因",
            "故障类型": "电气",
            "优先级": 1,
            "关键指标": "配电柜接线"
        },
        {
            "检查项目ID": "ELEC_003",
            "操作类型": "现场查看",
            "检查项目名称": "现场电压状态检查",
            "参数定义": "电压状态:枚举值(正常/缺相/欠压/过压),电压值:数值型(V)",
            "判断条件": "电压状态!=正常时为异常",
            "结果模板": "现场电压状态:{VOLTAGE_STATUS},电压值:{VOLTAGE_VALUE}V",
            "推理模板_正常": "现场电压正常无缺相或欠过电压，排除现场供电故障的可能性",
            "推理模板_异常": "现场电压存在缺相或欠过电压，可能是导致安全阀关断的原因",
            "故障类型": "电气",
            "优先级": 1,
            "关键指标": "现场电压"
        },
        {
            "检查项目ID": "ELEC_004",
            "操作类型": "电话询问",
            "检查项目名称": "变电所线路电压波动历史查询",
            "参数定义": "电压波动历史:布尔值(存在/不存在),跳闸事件:布尔值(有/无)",
            "判断条件": "电压波动历史=存在或跳闸事件=有时为异常",
            "结果模板": "线路电压波动历史:{VOLTAGE_FLUCTUATION},跳闸事件:{TRIP_EVENT}",
            "推理模板_正常": "变电所线路历史无电压波动事件，排除线路电压波动导致故障的可能性",
            "推理模板_异常": "变电所线路历史存在电压波动事件，可能是导致安全阀关断的原因",
            "故障类型": "电气",
            "优先级": 2,
            "关键指标": "电压波动历史"
        },
        {
            "检查项目ID": "ELEC_005",
            "操作类型": "现场查看",
            "检查项目名称": "多井场同时关断模式检查",
            "参数定义": "同时关断井场数:数值型(个),通信链路隔离:布尔值(是/否)",
            "判断条件": "同时关断井场数>1且通信链路隔离=是时为异常",
            "结果模板": "同时关断井场数:{WELL_COUNT}个,通信链路隔离:{ISOLATION_STATUS}",
            "推理模板_正常": "单一井场关断或通信链路未隔离，需进一步从其他方面判断故障来源",
            "推理模板_异常": "多个通信隔离井场同时关断，可排除单一硬件故障，故障大概率出自电气方面",
            "故障类型": "电气",
            "优先级": 3,
            "关键指标": "多井场同时关断"
        }
    ]
    
    return inspection_data

def generate_optimized_excel():
    """生成优化后的Excel文件"""
    
    # 创建优化后的数据          
    data = create_optimized_inspection_data()
    
    # 转换为DataFrame
    df = pd.DataFrame(data)
    
    # 保存为Excel文件
    output_file = "设备排查指标及推论_优化版.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主数据表
        df.to_excel(writer, sheet_name='检查项目清单', index=False)
        
        # 创建参数定义说明表
        param_definitions = [
            {"参数类型": "压力类", "单位": "MPa/kPa/psi", "说明": "各类压力参数，包括分离器压力、安全阀压力等"},
            {"参数类型": "温度类", "单位": "℃", "说明": "温度相关参数，如井场出口温度"},
            {"参数类型": "电压类", "单位": "V", "说明": "电气系统电压参数"},
            {"参数类型": "状态类", "单位": "布尔值", "说明": "设备状态参数，如开关状态、完好性等"},
            {"参数类型": "计数类", "单位": "个/次", "说明": "数量统计参数"}
        ]
        
        param_df = pd.DataFrame(param_definitions)
        param_df.to_excel(writer, sheet_name='参数定义说明', index=False)
        
        # 创建故障类型说明表
        fault_types = [
            {"故障类型": "自控", "说明": "自动控制系统相关故障", "典型原因": "压力超限、温度过高、ESD误动作"},
            {"故障类型": "井控", "说明": "井控设备硬件相关故障", "典型原因": "阀门损坏、管线泄漏、压力异常"},
            {"故障类型": "电气", "说明": "电气系统相关故障", "典型原因": "供电异常、接线故障、电压波动"}
        ]
        
        fault_df = pd.DataFrame(fault_types)
        fault_df.to_excel(writer, sheet_name='故障类型说明', index=False)
    
    print(f"优化后的Excel文件已生成: {output_file}")
    return output_file

if __name__ == "__main__":
    generate_optimized_excel()
