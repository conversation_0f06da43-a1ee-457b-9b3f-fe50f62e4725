#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的设备故障诊断思维链(COT)生成器
基于标准化的检查项目数据生成高质量的推理链
"""

import pandas as pd
import random
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import json

@dataclass
class InspectionItem:
    """检查项目数据类"""
    item_id: str
    operation_type: str
    item_name: str
    param_definition: str
    judgment_condition: str
    result_template: str
    reasoning_normal: str
    reasoning_abnormal: str
    fault_type: str
    priority: int

@dataclass
class InspectionResult:
    """检查结果数据类"""
    item: InspectionItem
    result_values: Dict[str, any]
    is_abnormal: bool
    formatted_result: str
    reasoning: str

class OptimizedCOTGenerator:
    """优化的COT生成器"""
    
    def __init__(self, excel_file: str = "设备排查指标及推论.xlsx"):
        """初始化生成器"""
        self.excel_file = excel_file
        self.inspection_items = self._load_inspection_items()
        self.parameter_ranges = self._define_parameter_ranges()
        
    def _load_inspection_items(self) -> Dict[str, List[InspectionItem]]:
        """加载检查项目数据"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='检查项目清单')
            items_by_type = {"自控": [], "井控": [], "电气": []}
            
            for _, row in df.iterrows():
                item = InspectionItem(
                    item_id=row['检查项目ID'],
                    operation_type=row['操作类型'],
                    item_name=row['检查项目名称'],
                    param_definition=row['参数定义'],
                    judgment_condition=row['判断条件'],
                    result_template=row['结果模板'],
                    reasoning_normal=row['推理模板_正常'],
                    reasoning_abnormal=row['推理模板_异常'],
                    fault_type=row['故障类型'],
                    priority=row['优先级'],
                )
                items_by_type[row['故障类型']].append(item)
                
            return items_by_type
            
        except Exception as e:
            print(f"加载Excel文件失败: {e}")
            return {"自控": [], "井控": [], "电气": []}
    
    def _define_parameter_ranges(self) -> Dict[str, Dict]:
        """定义参数范围"""
        return {
            "pressure": {"normal": (5, 14), "abnormal": (15, 25), "unit": "MPa"},
            "downhole_pressure": {"normal": (5000, 18000), "abnormal": (20001, 25000), "unit": "kPa"},
            "surface_pressure": {"normal": (2000, 5000), "abnormal": (5501, 7000), "unit": "kPa"},
            "fusible_pressure": {"normal": (55, 80), "abnormal": (30, 49), "unit": "psi"},
            "temperature": {"normal": (25, 85), "abnormal": (90, 115), "unit": "℃"},
            "voltage": {"normal": (220, 240), "abnormal": (200, 215), "unit": "V"}
        }
    
    def _generate_parameter_value(self, param_type: str, is_abnormal: bool = False) -> Tuple[float, str]:
        """生成参数值"""
        if param_type not in self.parameter_ranges:
            return 0.0, ""
            
        config = self.parameter_ranges[param_type]
        range_key = "abnormal" if is_abnormal else "normal"
        min_val, max_val = config[range_key]
        value = round(random.uniform(min_val, max_val), 1)
        
        return value, config["unit"]
    
    def _generate_inspection_result(self, item: InspectionItem, is_fault_item: bool = False) -> InspectionResult:
        """生成检查结果"""
        result_values = {}
        is_abnormal = is_fault_item
        
        # 根据参数定义生成具体的检查结果
        if "压力" in item.param_definition:
            if "分离器" in item.item_name:
                pressure_val, unit = self._generate_parameter_value("pressure", is_abnormal)
                result_values["PRESSURE_VALUE"] = pressure_val
                limit_status = "超限" if pressure_val >= 15 else "正常"
                result_values["LIMIT_STATUS"] = limit_status
                is_abnormal = pressure_val >= 15
                
            elif "井下" in item.item_name:
                pressure_val, unit = self._generate_parameter_value("downhole_pressure", is_abnormal)
                result_values["DOWNHOLE_PRESSURE"] = pressure_val
                limit_status = "超限" if pressure_val > 20000 else "正常"
                result_values["LIMIT_STATUS"] = limit_status
                is_abnormal = pressure_val > 20000
                
            elif "地面" in item.item_name:
                pressure_val, unit = self._generate_parameter_value("surface_pressure", is_abnormal)
                result_values["SURFACE_PRESSURE"] = pressure_val
                limit_status = "超限" if pressure_val > 5500 else "正常"
                result_values["LIMIT_STATUS"] = limit_status
                is_abnormal = pressure_val > 5500
                
            elif "易熔塞" in item.item_name:
                pressure_val, unit = self._generate_parameter_value("fusible_pressure", is_abnormal)
                result_values["FUSIBLE_PRESSURE"] = pressure_val
                joint_status = "损坏" if is_abnormal else "完好"
                result_values["JOINT_STATUS"] = joint_status
                is_abnormal = pressure_val <= 50 or joint_status == "损坏"
                
        elif "温度" in item.param_definition:
            temp_val, unit = self._generate_parameter_value("temperature", is_abnormal)
            result_values["TEMPERATURE"] = temp_val
            limit_status = "超限" if temp_val > 95 else "正常"
            result_values["LIMIT_STATUS"] = limit_status
            is_abnormal = temp_val > 95






    
    
        elif "电压" in item.param_definition:
            voltage_val, unit = self._generate_parameter_value("voltage", is_abnormal)
            result_values["VOLTAGE"] = voltage_val
            relay_status = "松动" if is_abnormal else "正常"
            result_values["RELAY_STATUS"] = relay_status
            is_abnormal = voltage_val < 220 or voltage_val > 240 or relay_status == "松动"
            
        elif "布尔值" in item.param_definition:
            # 处理布尔类型参数
            if "ESD按钮" in item.item_name:
                button_status = "按下" if is_abnormal else "未按下"
                result_values["BUTTON_STATUS"] = button_status
                is_abnormal = button_status == "按下"
                
            elif "命令" in item.item_name:
                command_status = "存在" if is_abnormal else "不存在"
                result_values["COMMAND_STATUS"] = command_status
                result_values["REMOTE_COMMAND_STATUS"] = command_status
                is_abnormal = command_status == "存在"
                
            elif "完好" in item.param_definition or "外漏" in item.param_definition:
                valve_status = "损坏" if is_abnormal else "完好"
                leak_status = "有漏" if is_abnormal else "无漏"
                result_values["VALVE_STATUS"] = valve_status
                result_values["LEAK_STATUS"] = leak_status
                result_values["PIPELINE_STATUS"] = valve_status
                is_abnormal = valve_status == "损坏" or leak_status == "有漏"
        
        # 格式化结果文本
        formatted_result = item.result_template
        for key, value in result_values.items():
            formatted_result = formatted_result.replace(f"{{{key}}}", str(value))
        
        # 选择推理模板
        reasoning = item.reasoning_abnormal if is_abnormal else item.reasoning_normal
        
        return InspectionResult(
            item=item,
            result_values=result_values,
            is_abnormal=is_abnormal,
            formatted_result=formatted_result,
            reasoning=reasoning
        )
    
    def generate_fault_diagnosis_cot(self, fault_type: str = None, num_steps: int = 5) -> Dict:
        """生成故障诊断思维链"""

        # 随机选择故障类型（如果未指定）
        if fault_type is None:
            fault_type = random.choice(["自控", "井控", "电气"])

        # 获取该类型的检查项目
        available_items = self.inspection_items.get(fault_type, [])
        if not available_items:
            return {"error": f"没有找到{fault_type}类型的检查项目"}

        # 随机选择检查步骤
        selected_items = random.sample(available_items, min(num_steps, len(available_items)))

        # 随机选择一个作为故障项目
        fault_item_index = random.randint(0, len(selected_items) - 1)

        # 生成检查结果并构建思维链
        cot_steps = []
        fault_found = False
        fault_cause = None

        # 逐步生成检查结果，当发现异常时停止后续检查
        for i, item in enumerate(selected_items):
            is_fault_item = (i == fault_item_index)
            result = self._generate_inspection_result(item, is_fault_item)

            step = {
                "步骤": i + 1,
                "检查项目": result.item.item_name,
                "操作类型": result.item.operation_type,
                "检查结果": result.formatted_result,
                "推理分析": result.reasoning,
                "是否异常": result.is_abnormal,
                "故障类型": result.item.fault_type
            }

            if result.is_abnormal and not fault_found:
                fault_found = True
                fault_cause = result.item.item_name
                step["结论"] = f"发现故障原因：{fault_cause}"
                cot_steps.append(step)
                # 发现异常后立即停止生成后续检查项目
                break
            elif not result.is_abnormal:
                step["结论"] = "排除此项故障原因"
                cot_steps.append(step)

        # 生成最终结论
        if fault_found:
            final_conclusion = f"通过系统性排查，确定故障原因为：{fault_cause}。建议立即进行相应的维修或更换操作。"
        else:
            final_conclusion = "经过排查，未发现明确的故障原因，建议扩大排查范围或进行更深入的检测。"

        return {
            "故障类型": fault_type,
            "检查步骤数": len(cot_steps),
            "思维链步骤": cot_steps,
            "最终结论": final_conclusion,
            "故障发现": fault_found,
            "故障原因": fault_cause,
            "生成时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def generate_multiple_scenarios(self, num_scenarios: int = 3) -> List[Dict]:
        """生成多个故障诊断场景"""
        scenarios = []
        fault_types = ["自控", "井控", "电气"]
        
        for i in range(num_scenarios):
            fault_type = fault_types[i % len(fault_types)]
            scenario = self.generate_fault_diagnosis_cot(fault_type)
            scenario["场景编号"] = i + 1
            scenarios.append(scenario)
        
        return scenarios
    
    def export_to_json(self, scenarios: List[Dict], filename: str = None) -> str:
        """导出场景到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"故障诊断COT_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(scenarios, f, ensure_ascii=False, indent=2)
        
        return filename

def main():
    """主函数"""
    print("=== 优化的设备故障诊断COT生成器 ===")
    
    # 创建生成器实例
    generator = OptimizedCOTGenerator()
    
    # 生成多个诊断场景
    print("正在生成故障诊断场景...")
    scenarios = generator.generate_multiple_scenarios(10)
    
    # 显示生成的场景
    for scenario in scenarios:
        print(f"\n--- 场景 {scenario['场景编号']}: {scenario['故障类型']}类故障 ---")
        print(f"检查步骤数: {scenario['检查步骤数']}")
        print(f"故障发现: {'是' if scenario['故障发现'] else '否'}")
        if scenario['故障发现']:
            print(f"故障原因: {scenario['故障原因']}")
        print(f"最终结论: {scenario['最终结论']}")
    
    # 导出到JSON文件
    json_file = generator.export_to_json(scenarios)
    print(f"\n场景已导出到: {json_file}")

if __name__ == "__main__":
    main()
