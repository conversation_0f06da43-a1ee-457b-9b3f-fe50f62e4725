#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取Word文档内容的工具脚本
"""

try:
    from docx import Document
    import sys
    
    def read_docx(file_path):
        """读取Word文档内容"""
        try:
            doc = Document(file_path)
            content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text)
            
            return '\n'.join(content)
        except Exception as e:
            return f"读取文档失败: {e}"
    
    if __name__ == "__main__":
        content = read_docx("COT示例.docx")
        print(content)
        
except ImportError:
    print("需要安装python-docx库: pip install python-docx")
    print("或者请提供文档的文本内容")
